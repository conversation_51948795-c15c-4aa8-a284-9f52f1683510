## Summary
This commit adds a test Python script for monitoring demonstration purposes.

## Technical Details
The only file changed in this commit is `test_script.py`, which was created from scratch. The script contains a single line of code: `print(123)`. This suggests that the script is intended to be a basic testing mechanism, possibly serving as a starting point for more complex monitoring demonstrations.

## Impact Assessment
This commit has a low impact on the codebase and system functionality. The addition of this test script does not affect any existing features or interfaces, nor does it introduce new security risks. However, it may provide a useful tool for testing and debugging purposes in the future.

## Code Review Recommendation
Low complexity and risk level. This commit is unlikely to introduce bugs or have significant security implications. As such, code review is not strictly necessary for this commit. Nevertheless, a cursory review could still be beneficial to ensure that the script is correctly written and does not inadvertently reveal sensitive information.

## Documentation Impact
This commit has no direct impact on documentation. The `test_script.py` file is likely intended for internal use only and may not require documentation updates. However, if this script becomes an integral part of a larger monitoring or testing framework, documentation updates might be necessary in the future to provide context and usage guidance.

## Recommendations
Future commits could consider adding more functionality to this test script, such as logging, error handling, or integration with existing monitoring tools. Additionally, it would be beneficial to add comments or docstrings to the script to explain its purpose and usage.