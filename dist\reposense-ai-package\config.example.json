{"_comment": "RepoSense AI Configuration Example", "_description": "Copy this file to data/config.json and customize for your environment", "repositories": [{"_comment": "Example repository configuration - remove this entry and add your own", "id": "example-repo-1", "name": "Main Project Repository", "url": "https://svn.example.com/repos/main-project", "username": "your-svn-username", "password": "your-svn-password", "enabled": true, "last_revision": 0, "branch_path": "trunk", "monitor_all_branches": false, "assigned_users": ["user-id-1", "user-id-2"], "email_recipients": ["<EMAIL>", "<EMAIL>"]}, {"_comment": "Another example - you can monitor multiple repositories", "id": "example-repo-2", "name": "Documentation Repository", "url": "https://svn.example.com/repos/docs", "username": null, "password": null, "enabled": false, "last_revision": 0, "branch_path": null, "monitor_all_branches": true, "assigned_users": ["user-id-3"], "email_recipients": ["<EMAIL>", "<EMAIL>"]}], "_users_section": "=== User Management ===", "users": [{"_comment": "Example admin user - customize for your environment", "id": "user-id-1", "username": "admin", "email": "<EMAIL>", "full_name": "System Administrator", "role": "admin", "enabled": true, "receive_all_notifications": true, "repository_subscriptions": [], "phone": "******-0100", "department": "IT", "created_date": "2024-01-01T00:00:00Z", "last_modified": "2024-01-01T00:00:00Z"}, {"_comment": "Example developer user", "id": "user-id-2", "username": "developer", "email": "<EMAIL>", "full_name": "Lead Developer", "role": "developer", "enabled": true, "receive_all_notifications": false, "repository_subscriptions": ["example-repo-1"], "phone": "******-0101", "department": "Engineering", "created_date": "2024-01-01T00:00:00Z", "last_modified": "2024-01-01T00:00:00Z"}], "_users_note": "User roles: admin, manager, developer, viewer", "_ollama_section": "=== Ollama AI Configuration ===", "ollama_host": "http://ollama:11434", "ollama_model": "llama2", "_ollama_note": "Available models: llama2, codellama, mistral, etc. Model will be downloaded on first use", "_monitoring_section": "=== Monitoring Settings ===", "check_interval": 300, "_check_interval_note": "How often to check for changes (in seconds). 300 = 5 minutes", "_svn_server_section": "=== SVN Server Configuration ===", "svn_server_url": "http://sundc:81/svn", "svn_server_username": null, "svn_server_password": null, "svn_server_type": "auto", "_svn_server_type_options": "auto, visualsvn, apache, standard, custom", "_svn_server_note": "Base SVN server settings for repository discovery. Individual repositories can override these credentials.", "_svn_server_type_note": "Server type affects discovery method: 'auto' detects automatically, 'visualsvn' uses XML parsing, 'standard' uses svn list recursively", "_email_section": "=== Email Configuration ===", "smtp_host": "smtp.gmail.com", "smtp_port": 587, "smtp_username": "<EMAIL>", "smtp_password": "your-app-password", "email_from": "<EMAIL>", "email_recipients": ["<EMAIL>", "<EMAIL>"], "send_emails": false, "_email_note": "Global recipients get emails for ALL repositories. Repository-specific recipients are configured per repository.", "_email_structure": "Final recipients = global recipients + repository-specific recipients (no duplicates)", "_output_section": "=== Output and Storage ===", "output_dir": "/app/data", "_output_note": "Directory where generated docs and emails are stored", "_features_section": "=== Feature Toggles ===", "generate_docs": true, "_generate_docs_note": "Generate AI-powered documentation for code changes", "_web_section": "=== Web Interface ===", "web_enabled": true, "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "change-this-secret-key-in-production-environment", "_web_note": "Web interface for configuration and monitoring"}