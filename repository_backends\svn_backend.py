#!/usr/bin/env python3
"""
SVN repository backend plugin
Handles all interactions with SVN repositories
"""

import logging
import subprocess
import xml.etree.ElementTree as ET
from typing import List, Optional, Dict, Any, Iterator
from urllib.parse import urljoin
from datetime import datetime
import requests
import re

from .base import RepositoryBackend, RepositoryInfo
from models import CommitInfo, RepositoryConfig


class SVNBackend(RepositoryBackend):
    """SVN repository backend implementation"""
    
    def __init__(self, config):
        super().__init__(config)
        self.logger = logging.getLogger(__name__)
    
    @property
    def backend_type(self) -> str:
        return 'svn'
    
    def get_command_base(self, repo: RepositoryConfig) -> List[str]:
        """Get base SVN command with authentication for a specific repository"""
        cmd = ["svn"]
        if repo.username:
            cmd.extend(["--username", repo.username])
        if repo.password:
            cmd.extend(["--password", repo.password])
        # Comprehensive SSL certificate handling
        cmd.extend([
            "--non-interactive",
            "--trust-server-cert",
            "--trust-server-cert-failures=unknown-ca,cn-mismatch,expired,not-yet-valid,other"
        ])
        return cmd
    
    def get_latest_revision(self, repo: RepositoryConfig) -> Optional[str]:
        """Get the latest revision number from SVN repository"""
        try:
            cmd = self.get_command_base(repo)
            cmd.extend(["info", repo.url, "--xml"])

            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            root = ET.fromstring(result.stdout)
            commit_elem = root.find(".//commit")
            if commit_elem is not None:
                revision_str = commit_elem.get("revision")
                if revision_str:
                    self.logger.info(f"Latest revision for {repo.name}: {revision_str}")
                    return revision_str

            self.logger.error(f"No commit information found for {repo.name}")
            return None

        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error getting latest revision for {repo.name}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error getting revision for {repo.name}: {e}")
            return None
    
    def get_commit_info(self, repo: RepositoryConfig, revision: str) -> Optional[CommitInfo]:
        """Get detailed information about a specific commit"""
        try:
            # Get commit log
            cmd = self.get_command_base(repo)
            cmd.extend(["log", repo.url, "-r", revision, "--xml", "-v"])

            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            root = ET.fromstring(result.stdout)

            entry = root.find("logentry")
            if entry is None:
                return None

            # Extract commit information
            revision_attr = entry.get("revision", revision)
            author = entry.findtext("author", "Unknown")
            date = entry.findtext("date", "")
            message = entry.findtext("msg", "")

            # Extract changed paths
            changed_paths = []
            paths_elem = entry.find("paths")
            if paths_elem is not None:
                for path_elem in paths_elem.findall("path"):
                    if path_elem.text:
                        changed_paths.append(path_elem.text)

            # Get diff
            diff = self.get_diff(repo, revision) or ""

            return CommitInfo(
                revision=revision_attr,
                author=author,
                date=date,
                message=message,
                changed_paths=changed_paths,
                diff=diff,
                repository_id=repo.id,
                repository_name=repo.name
            )

        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error getting commit info for {repo.name} r{revision}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error getting commit info for {repo.name} r{revision}: {e}")
            return None
    
    def get_diff(self, repo: RepositoryConfig, revision: str) -> Optional[str]:
        """Get the diff for a specific revision"""
        try:
            cmd = self.get_command_base(repo)
            cmd.extend(["diff", repo.url, "-c", revision])

            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return result.stdout

        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error getting diff for {repo.name} r{revision}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error getting diff for {repo.name} r{revision}: {e}")
            return None
    
    def test_connection(self, repo: RepositoryConfig) -> bool:
        """Test if the repository is accessible"""
        try:
            cmd = self.get_command_base(repo)
            cmd.extend(["info", repo.url])
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            return result.returncode == 0
            
        except Exception as e:
            self.logger.error(f"Error testing connection to {repo.name}: {e}")
            return False
    
    def discover_repositories(self, base_url: str, username: Optional[str] = None, 
                            password: Optional[str] = None, max_depth: int = 3) -> List[RepositoryInfo]:
        """Discover repositories from an SVN server"""
        discovered_repos = []
        
        try:
            self.logger.info(f"Starting repository discovery from {base_url}")
            discovered_repos = self._discover_recursive(base_url, username, password, 0, max_depth)
            self.logger.info(f"Discovered {len(discovered_repos)} repositories")

        except Exception as e:
            self.logger.error(f"Error during repository discovery: {e}")
            self.logger.error(f"Exception details: {str(e)}")
            
        return discovered_repos
    
    def _discover_recursive(self, url: str, username: Optional[str], password: Optional[str],
                          current_depth: int, max_depth: int) -> List[RepositoryInfo]:
        """Recursively discover repositories"""
        repositories = []
        
        if current_depth >= max_depth:
            return repositories
            
        try:
            self.logger.debug(f"Checking directory: {url} (depth {current_depth})")

            # Special handling for different SVN server types at root level
            if current_depth == 0:
                server_type = self._detect_server_type(url, username, password)
                self.logger.debug(f"Detected server type: {server_type} at {url}")

                if server_type in ['visualsvn', 'apache_dav']:
                    return self._discover_xml_repositories(url, username, password)
                elif server_type == 'standard':
                    # Continue with standard recursive discovery
                    pass

            entries = self._get_svn_list(url, username, password)
            self.logger.debug(f"Found {len(entries)} entries in {url}")

            for entry in entries:
                entry_url = urljoin(url.rstrip('/') + '/', entry['name'])
                
                if entry['kind'] == 'dir':
                    # Check if this directory is a repository root
                    if self._is_repository_root(entry_url, username, password):
                        repo_info = RepositoryInfo(
                            name=entry['name'],
                            url=entry_url,
                            path=entry_url.replace(url.rstrip('/'), '').lstrip('/'),
                            last_revision=str(entry.get('commit_revision', 0)),
                            last_author=entry.get('commit_author', ''),
                            last_date=entry.get('commit_date', ''),
                            size=entry.get('size', 0),
                            repository_type='svn'
                        )
                        repositories.append(repo_info)
                        self.logger.debug(f"Found repository: {entry_url}")
                    else:
                        # Recurse into subdirectories
                        sub_repos = self._discover_recursive(entry_url, username, password, 
                                                           current_depth + 1, max_depth)
                        repositories.extend(sub_repos)
                        
        except Exception as e:
            self.logger.warning(f"Could not list directory {url}: {e}")
            
        return repositories

    def _test_svn_info(self, url: str, username: Optional[str], password: Optional[str]) -> bool:
        """Test if a URL is a valid SVN repository by running svn info with protocol fallback"""
        urls_to_try = [url]

        # Add protocol fallback URLs
        if url.startswith('https://'):
            urls_to_try.append(url.replace('https://', 'http://'))
        elif url.startswith('http://'):
            urls_to_try.append(url.replace('http://', 'https://'))

        for try_url in urls_to_try:
            try:
                cmd = ['svn', 'info', '--xml', try_url]

                if username:
                    cmd.extend(['--username', username])
                if password:
                    cmd.extend(['--password', password])

                cmd.extend([
                    '--non-interactive',
                    '--trust-server-cert',
                    '--trust-server-cert-failures=unknown-ca,cn-mismatch,expired,not-yet-valid,other'
                ])

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
                if result.returncode == 0:
                    self.logger.debug(f"SVN info test succeeded with URL: {try_url}")
                    return True
                else:
                    self.logger.debug(f"SVN info test failed for URL: {try_url} - {result.stderr}")

            except Exception as e:
                self.logger.debug(f"SVN info test error for URL: {try_url} - {e}")
                continue

        return False

    def _detect_server_type(self, url: str, username: Optional[str], password: Optional[str]) -> str:
        """Detect the type of SVN server for optimal discovery strategy"""
        try:
            import requests

            # Check if config specifies a server type
            if hasattr(self, 'config') and self.config:
                server_type = getattr(self.config, 'svn_server_type', 'auto')
                if server_type != 'auto':
                    self.logger.debug(f"Using configured server type: {server_type}")
                    return server_type

            # Auto-detect server type
            auth = (username, password) if username and password else None
            response = requests.head(url, auth=auth, timeout=10)
            server_header = response.headers.get('Server', '').lower()

            if 'visualsvn' in server_header:
                return 'visualsvn'
            elif 'apache' in server_header:
                # Try to determine if it's Apache with DAV/SVN
                try:
                    get_response = requests.get(url, auth=auth, timeout=10)
                    if '<dir name=' in get_response.text:
                        return 'apache_dav'
                except Exception:
                    pass
                return 'apache'
            else:
                return 'standard'

        except Exception as e:
            self.logger.debug(f"Error detecting server type: {e}")
            return 'standard'

    def _discover_xml_repositories(self, base_url: str, username: Optional[str], password: Optional[str]) -> List[RepositoryInfo]:
        """Discover repositories by parsing XML directory listing (VisualSVN, Apache DAV, etc.)"""
        repositories = []

        try:
            import requests
            import xml.etree.ElementTree as ET

            # Get the XML directory listing
            auth = (username, password) if username and password else None
            response = requests.get(base_url, auth=auth, timeout=30)

            if response.status_code != 200:
                self.logger.warning(f"Could not access SVN server at {base_url}: HTTP {response.status_code}")
                return repositories

            self.logger.debug(f"XML content length: {len(response.text)}")
            self.logger.debug(f"XML content preview: {response.text[:200]}...")

            # Parse the XML content to find directory entries
            try:
                # The content is SVN XML format with <dir> elements
                # Need to handle the XML properly - it might already be well-formed
                xml_content = response.text.strip()
                if not xml_content.startswith('<?xml') and not xml_content.startswith('<'):
                    # If it doesn't start with XML declaration or tag, wrap it
                    xml_content = f"<root>{xml_content}</root>"

                root = ET.fromstring(xml_content)
                dir_elements = root.findall('.//dir')
                self.logger.debug(f"Found {len(dir_elements)} directory entries in XML")

                for dir_elem in dir_elements:
                    repo_name = dir_elem.get('name')
                    if repo_name:
                        repo_url = f"{base_url.rstrip('/')}/{repo_name}"
                        self.logger.debug(f"Testing potential repository: {repo_url}")

                        # Test if this is actually a valid SVN repository
                        if self._test_svn_info(repo_url, username, password):
                            self.logger.debug(f"Found XML-listed repository: {repo_url}")

                            # Get repository info
                            repo_info = RepositoryInfo(
                                name=repo_name,
                                url=repo_url,
                                path=repo_name,
                                last_revision="0",
                                last_author="",
                                last_date="",
                                size=0,
                                repository_type='svn'
                            )
                            repositories.append(repo_info)
                        else:
                            self.logger.debug(f"Directory {repo_url} is not a valid SVN repository")

            except ET.ParseError as e:
                self.logger.error(f"Error parsing XML from SVN server: {e}")

        except ImportError:
            self.logger.error("requests library is required for XML-based repository discovery")
        except Exception as e:
            self.logger.error(f"Error discovering XML-listed repositories: {e}")

        return repositories

    def _get_svn_list(self, url: str, username: Optional[str], password: Optional[str]) -> List[Dict]:
        """Get SVN directory listing with protocol fallback"""
        urls_to_try = [url]

        # Add protocol fallback URLs
        if url.startswith('https://'):
            urls_to_try.append(url.replace('https://', 'http://'))
        elif url.startswith('http://'):
            urls_to_try.append(url.replace('http://', 'https://'))

        last_error = None

        for try_url in urls_to_try:
            cmd = ['svn', 'list', '--xml', try_url]

            if username:
                cmd.extend(['--username', username])
            if password:
                cmd.extend(['--password', password])

            cmd.extend([
                '--non-interactive',
                '--trust-server-cert',
                '--trust-server-cert-failures=unknown-ca,cn-mismatch,expired,not-yet-valid,other'
            ])

            try:
                self.logger.debug(f"Running SVN command: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

                if result.returncode == 0:
                    self.logger.debug(f"SVN command succeeded with URL: {try_url}")
                    return self._parse_svn_list_xml(result.stdout)
                else:
                    error_msg = f"SVN command failed: {result.stderr}"
                    self.logger.debug(f"SVN command failed with return code {result.returncode} for URL: {try_url}")
                    self.logger.debug(f"SVN stderr: {result.stderr}")
                    self.logger.debug(f"SVN stdout: {result.stdout}")
                    last_error = error_msg

            except subprocess.TimeoutExpired:
                error_msg = "SVN command timed out"
                self.logger.debug(f"SVN command timed out for URL: {try_url}")
                last_error = error_msg
                continue
            except Exception as e:
                error_msg = f"Error running SVN command: {e}"
                self.logger.debug(f"SVN command error for URL: {try_url}: {e}")
                last_error = error_msg
                continue

        # If we get here, all URLs failed
        if last_error:
            raise Exception(last_error)
        else:
            raise Exception("SVN command failed for all attempted URLs")

    def _parse_svn_list_xml(self, xml_output: str) -> List[Dict]:
        """Parse SVN list XML output"""
        entries = []

        try:
            root = ET.fromstring(xml_output)

            for entry in root.findall(".//entry"):
                name = entry.findtext("name", "")
                kind = entry.get("kind", "")
                size = entry.findtext("size")

                # Parse commit info if available
                commit = entry.find("commit")
                commit_info = {}
                if commit is not None:
                    commit_info = {
                        'commit_revision': int(commit.get("revision", 0)),
                        'commit_author': commit.findtext("author", ""),
                        'commit_date': commit.findtext("date", "")
                    }

                entry_info = {
                    'name': name,
                    'kind': kind,
                    'size': int(size) if size else 0,
                    **commit_info
                }

                entries.append(entry_info)

        except ET.ParseError as e:
            self.logger.error(f"Error parsing SVN XML output: {e}")

        return entries

    def _is_repository_root(self, url: str, username: Optional[str], password: Optional[str]) -> bool:
        """Check if a URL is a repository root by looking for standard SVN structure or testing SVN info"""
        try:
            # First try to get SVN info directly - this is the most reliable way
            if self._test_svn_info(url, username, password):
                self.logger.debug(f"Confirmed {url} is a repository via svn info")
                return True

            # Fallback: check for standard SVN layout (trunk, branches, tags)
            entries = self._get_svn_list(url, username, password)
            entry_names = [entry['name'] for entry in entries if entry['kind'] == 'dir']
            self.logger.debug(f"Checking if {url} is repository root. Found directories: {entry_names}")

            # Check for standard SVN layout (trunk, branches, tags)
            has_trunk = 'trunk' in entry_names
            has_branches = 'branches' in entry_names
            has_tags = 'tags' in entry_names

            is_repo = has_trunk or (has_branches and has_tags)
            self.logger.debug(f"Repository check for {url}: trunk={has_trunk}, branches={has_branches}, tags={has_tags}, is_repo={is_repo}")

            return is_repo

        except Exception as e:
            self.logger.debug(f"Error checking repository root {url}: {e}")
            return False

    def get_repository_info(self, repo: RepositoryConfig) -> Dict[str, Any]:
        """Get detailed information about a repository"""
        try:
            cmd = ['svn', 'info', '--xml', repo.url]

            if repo.username:
                cmd.extend(['--username', repo.username])
            if repo.password:
                cmd.extend(['--password', repo.password])

            cmd.extend([
                '--non-interactive',
                '--trust-server-cert',
                '--trust-server-cert-failures=unknown-ca,cn-mismatch,expired,not-yet-valid,other'
            ])

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                raise Exception(f"SVN info failed: {result.stderr}")

            return self._parse_svn_info_xml(result.stdout)

        except Exception as e:
            self.logger.error(f"Error getting repository info: {e}")
            return {}

    def _parse_svn_info_xml(self, xml_output: str) -> Dict[str, Any]:
        """Parse SVN info XML output"""
        info = {}

        try:
            root = ET.fromstring(xml_output)
            entry = root.find("entry")

            if entry is not None:
                info['url'] = entry.findtext("url", "")
                info['root'] = entry.findtext("repository/root", "")
                info['uuid'] = entry.findtext("repository/uuid", "")

                # Commit info
                commit = entry.find("commit")
                if commit is not None:
                    info['revision'] = commit.get("revision", "")
                    info['author'] = commit.findtext("author", "")
                    info['date'] = commit.findtext("date", "")

        except ET.ParseError as e:
            self.logger.error(f"Error parsing SVN info XML: {e}")

        return info

    def validate_repository_config(self, repo: RepositoryConfig) -> List[str]:
        """Validate SVN repository configuration"""
        errors = super().validate_repository_config(repo)

        # SVN-specific validation
        if repo.url and not (repo.url.startswith('http://') or
                           repo.url.startswith('https://') or
                           repo.url.startswith('svn://') or
                           repo.url.startswith('file://')):
            errors.append("SVN URL must start with http://, https://, svn://, or file://")

        return errors

    # Optimized historical scanning methods for SVN

    def get_revision_range(self, repo: RepositoryConfig, start_revision: Optional[str] = None,
                          end_revision: Optional[str] = None) -> List[str]:
        """
        Get list of SVN revisions in a range efficiently

        Args:
            repo: Repository configuration
            start_revision: Starting revision (inclusive), None for first revision
            end_revision: Ending revision (inclusive), None for latest revision

        Returns:
            List of revision identifiers in chronological order
        """
        try:
            # Get the actual latest revision from the repository
            latest = self.get_latest_revision(repo)
            if not latest:
                return []

            latest_num = int(latest)

            # Get end revision if not specified
            if not end_revision:
                end_revision = latest

            # Default start to revision 1 if not specified
            if not start_revision:
                start_revision = "1"

            # Convert to integers for range generation
            start_num = int(start_revision)
            end_num = int(end_revision)

            # SVN repositories start at revision 1, not 0
            if start_num < 1:
                self.logger.info(f"Start revision {start_num} adjusted to 1 (SVN minimum) for {repo.name}")
                start_num = 1

            # Validate that start revision is not greater than latest
            if start_num > latest_num:
                self.logger.warning(f"Start revision {start_num} is greater than latest revision {latest_num} for {repo.name}")
                return []

            # Clamp end revision to the actual latest revision
            if end_num > latest_num:
                self.logger.info(f"End revision {end_num} clamped to latest revision {latest_num} for {repo.name}")
                end_num = latest_num

            # Generate revision list (only existing revisions)
            return [str(rev) for rev in range(start_num, end_num + 1)]

        except (ValueError, TypeError) as e:
            self.logger.error(f"Error generating revision range for {repo.name}: {e}")
            return []

    def get_revisions_by_date_range(self, repo: RepositoryConfig, start_date: datetime,
                                   end_date: datetime) -> List[str]:
        """
        Get list of SVN revisions within a date range efficiently using svn log

        Args:
            repo: Repository configuration
            start_date: Starting date (inclusive)
            end_date: Ending date (inclusive)

        Returns:
            List of revision identifiers in chronological order
        """
        try:
            cmd = self.get_command_base(repo)

            # Format dates for SVN (ISO format)
            start_str = start_date.strftime('%Y-%m-%d')
            end_str = end_date.strftime('%Y-%m-%d')

            # Use SVN log with date range
            cmd.extend([
                "log", repo.url,
                "-r", f"{{{start_str}}}:{{{end_str}}}",
                "--xml", "--quiet"  # quiet mode for just revision numbers
            ])

            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            root = ET.fromstring(result.stdout)

            # Extract revision numbers
            revisions = []
            for entry in root.findall("logentry"):
                revision = entry.get("revision")
                if revision:
                    revisions.append(revision)

            # Sort revisions numerically
            revisions.sort(key=int)
            return revisions

        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error getting revisions by date range for {repo.name}: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Unexpected error getting revisions by date for {repo.name}: {e}")
            return []

    def get_commit_batch(self, repo: RepositoryConfig, revisions: List[str]) -> Iterator[CommitInfo]:
        """
        Get commit information for multiple SVN revisions efficiently using batch log command

        Args:
            repo: Repository configuration
            revisions: List of revision identifiers

        Yields:
            CommitInfo objects for each valid revision
        """
        if not revisions:
            return

        try:
            # Process revisions in batches to avoid command line length limits
            batch_size = 50  # Reasonable batch size for SVN

            for i in range(0, len(revisions), batch_size):
                batch = revisions[i:i + batch_size]

                # For SVN, we need to use range syntax or individual revisions
                # Convert batch to range if consecutive, otherwise use individual calls
                if len(batch) > 1 and self._is_consecutive_range(batch):
                    # Use range syntax for consecutive revisions
                    revision_range = f"{batch[0]}:{batch[-1]}"
                else:
                    # Use individual revision calls for non-consecutive revisions
                    for revision in batch:
                        yield from self._get_single_commit(repo, revision)
                    continue

                cmd = self.get_command_base(repo)
                cmd.extend([
                    "log", repo.url,
                    "-r", revision_range,
                    "--xml", "-v"  # verbose mode for changed paths
                ])

                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                    root = ET.fromstring(result.stdout)

                    # Process each log entry
                    for entry in root.findall("logentry"):
                        revision = entry.get("revision")
                        if not revision:
                            continue

                        author = entry.findtext("author", "Unknown")
                        date = entry.findtext("date", "")
                        message = entry.findtext("msg", "")

                        # Extract changed paths
                        changed_paths = []
                        paths_elem = entry.find("paths")
                        if paths_elem is not None:
                            for path_elem in paths_elem.findall("path"):
                                if path_elem.text:
                                    changed_paths.append(path_elem.text)

                        # Get diff for this revision (this is expensive, consider making optional)
                        diff = self.get_diff(repo, revision) or ""

                        yield CommitInfo(
                            revision=revision,
                            author=author,
                            date=date,
                            message=message,
                            changed_paths=changed_paths,
                            diff=diff,
                            repository_id=repo.id,
                            repository_name=repo.name
                        )

                except subprocess.CalledProcessError as e:
                    self.logger.error(f"Error getting batch commits for {repo.name}: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"Unexpected error in batch commit processing for {repo.name}: {e}")

    def _is_consecutive_range(self, revisions: List[str]) -> bool:
        """Check if revisions form a consecutive range"""
        try:
            nums = [int(r) for r in revisions]
            nums.sort()
            for i in range(1, len(nums)):
                if nums[i] != nums[i-1] + 1:
                    return False
            return True
        except (ValueError, TypeError):
            return False

    def _get_single_commit(self, repo: RepositoryConfig, revision: str):
        """Get commit info for a single revision"""
        try:
            commit_info = self.get_commit_info(repo, revision)
            if commit_info:
                yield commit_info
        except Exception as e:
            self.logger.error(f"Error getting single commit {revision} for {repo.name}: {e}")

    def get_repository_statistics(self, repo: RepositoryConfig) -> Dict[str, Any]:
        """
        Get SVN repository statistics for historical scanning planning

        Args:
            repo: Repository configuration

        Returns:
            Dictionary with statistics like total_revisions, first_revision, etc.
        """
        try:
            # Get repository info
            info = self.get_repository_info(repo)
            latest_revision = info.get('revision')

            if not latest_revision:
                return {'error': 'Could not determine latest revision'}

            # Get first revision (usually 1 for SVN, but let's check)
            cmd = self.get_command_base(repo)
            cmd.extend([
                "log", repo.url,
                "--limit", "1",
                "-r", "1:HEAD",
                "--xml"
            ])

            try:
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                root = ET.fromstring(result.stdout)
                first_entry = root.find("logentry")
                first_revision = first_entry.get("revision") if first_entry is not None else "1"
            except:
                first_revision = "1"  # Default assumption for SVN

            # Calculate statistics
            if latest_revision and first_revision:
                total_revisions = int(latest_revision) - int(first_revision) + 1
            else:
                total_revisions = 0

            return {
                'backend_type': self.backend_type,
                'latest_revision': latest_revision,
                'first_revision': first_revision,
                'total_revisions': total_revisions,
                'repository_root': info.get('root', ''),
                'repository_uuid': info.get('uuid', ''),
                'url': info.get('url', repo.url)
            }

        except Exception as e:
            self.logger.error(f"Error getting repository statistics for {repo.name}: {e}")
            return {'error': str(e)}
