#!/bin/bash
# Copy SVN credentials from mounted host directory to container user directory

# Check if mounted credentials exist
if [ -d "/host-svn-credentials" ]; then
    echo "Copying SVN credentials from host..."

    # Remove existing .subversion directory to avoid permission conflicts
    rm -rf /home/<USER>/.subversion

    # Create .subversion directory
    mkdir -p /home/<USER>/.subversion

    # Copy all credential files
    cp -r /host-svn-credentials/* /home/<USER>/.subversion/

    # Set proper ownership first
    chown -R appuser:appuser /home/<USER>/.subversion

    # Set proper permissions
    chmod 755 /home/<USER>/.subversion
    chmod 644 /home/<USER>/.subversion/config
    chmod 644 /home/<USER>/.subversion/servers
    chmod 644 /home/<USER>/.subversion/README.txt
    chmod 755 /home/<USER>/.subversion/auth

    # Set permissions for auth subdirectories and files
    if [ -d "/home/<USER>/.subversion/auth" ]; then
        find /home/<USER>/.subversion/auth -type d -exec chmod 755 {} \;
        find /home/<USER>/.subversion/auth -type f -exec chmod 600 {} \;
    fi

    echo "SVN credentials copied successfully"
else
    echo "No SVN credentials found at /host-svn-credentials"

    # Create default .subversion directory and configuration
    mkdir -p /home/<USER>/.subversion
    chown -R appuser:appuser /home/<USER>/.subversion
fi

# Create or update SVN servers configuration for SSL handling
echo "Configuring SVN for SSL certificate handling..."
cat > /home/<USER>/.subversion/servers << 'EOF'
[global]
# Global server configuration options
http-timeout = 60
http-compression = yes
neon-debug-mask = 0
http-auth-types = basic;digest;negotiate
ssl-authority-files =
ssl-trust-default-ca = yes
ssl-client-cert-file =
ssl-client-cert-password =
store-passwords = yes
store-plaintext-passwords = yes

# SSL certificate handling - accept all certificate issues
ssl-ignore-unknown-ca = yes
ssl-ignore-invalid-date = yes
ssl-ignore-host-mismatch = yes

[groups]
# You can define server groups here

EOF

# Set proper ownership and permissions
chown appuser:appuser /home/<USER>/.subversion/servers
chmod 644 /home/<USER>/.subversion/servers

# Continue with the original command
exec "$@"
