## Summary
This commit adds two new prime number algorithms, the Sieve of Sundaram and the <PERSON>-<PERSON><PERSON> probabilistic primality test. The Sieve of Eratosthenes algorithm has been enhanced to include a type annotation for the primes list variable. The interactive mode has been updated with new commands (<PERSON><PERSON> and <PERSON><PERSON><PERSON>) to allow users to test individual numbers using different algorithms.

## Technical Details
The commit introduces two new algorithms for finding prime numbers:

1.  **Sieve of Sundaram**: This algorithm is an alternative to the Sieve of Eratosthenes and generates all odd primes up to a given limit. It uses a boolean array to mark composite numbers and then collects all prime numbers.
2.  **Miller-Rabin probabilistic primality test**: This algorithm is a probabilistic method for testing whether a number is prime or composite. It's much faster than the basic trial division method but has a small probability of error.

The Sieve of Eratosthenes algorithm has been updated to include a type annotation for the primes list variable, which makes it more readable and maintainable.

## Impact Assessment
This commit will have a positive impact on the codebase by providing multiple algorithms for different use cases and performance requirements. The new algorithms will allow users to choose the most suitable method based on their specific needs. The enhanced Sieve of Eratosthenes algorithm will improve the accuracy and efficiency of prime number calculations.

## Code Review Recommendation
Yes, this commit should be code reviewed due to its complexity and potential for introducing bugs. The commit introduces new algorithms and updates existing ones, which may affect system functionality or user experience if not implemented correctly.

## Documentation Impact
This commit affects documentation by updating the README file with information about the new algorithms and their usage. The interactive mode commands have also been updated, so the documentation should reflect these changes.

## Recommendations
Future commits could improve the usability of the Miller-Rabin algorithm by providing more detailed error messages or warnings when it returns a result that is not 100% accurate. Additionally, further testing and optimization of the algorithms could be done to improve their performance and accuracy.