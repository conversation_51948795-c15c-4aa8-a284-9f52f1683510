#!/usr/bin/env python3
"""
RepoSense AI Binary Entry Point
Modified version for standalone binary deployment with relative paths
"""

import sys
import os
import time
from pathlib import Path

# Get the directory where the executable is located
if getattr(sys, 'frozen', False):
    # Running as compiled binary
    app_dir = Path(sys.executable).parent
else:
    # Running as script
    app_dir = Path(__file__).parent

# Set up environment variables for relative paths
os.environ['REPOSENSE_AI_DATA_DIR'] = str(app_dir / 'data')
os.environ['REPOSENSE_AI_LOG_DIR'] = str(app_dir / 'logs')
os.environ['REPOSENSE_AI_CONFIG'] = str(app_dir / 'data' / 'config.json')

# Add the app directory to Python path so imports work
sys.path.insert(0, str(app_dir))

from monitor_service import MonitorService
from web_interface import WebInterface


def setup_directories():
    """Create necessary directories"""
    data_dir = app_dir / "data"
    logs_dir = app_dir / "logs"
    
    data_dir.mkdir(exist_ok=True)
    logs_dir.mkdir(exist_ok=True)
    
    return data_dir, logs_dir


def setup_config(data_dir):
    """Setup configuration file"""
    config_path = data_dir / "config.json"
    config_example = app_dir / "config.example.json"
    
    if not config_path.exists():
        if config_example.exists():
            print("📝 Creating initial configuration from example...")
            import shutil
            shutil.copy2(config_example, config_path)
            print(f"✅ Configuration created: {config_path}")
            print("🔧 Please customize the configuration file before starting monitoring.")
        else:
            print("⚠️  No configuration example found. Creating minimal config...")
            minimal_config = {
                "repositories": [],
                "ollama": {
                    "base_url": "http://localhost:11434",
                    "model": "llama2"
                },
                "web": {
                    "host": "0.0.0.0",
                    "port": 5000,
                    "debug": False
                },
                "email": {
                    "enabled": False
                },
                "users": []
            }
            import json
            with open(config_path, 'w') as f:
                json.dump(minimal_config, f, indent=2)
            print(f"✅ Minimal configuration created: {config_path}")
    
    return config_path


def main():
    """Main entry point for binary deployment"""
    print("🚀 RepoSense AI Starting...")
    print(f"📁 Application directory: {app_dir}")
    
    try:
        # Setup directories
        data_dir, logs_dir = setup_directories()
        print(f"📂 Data directory: {data_dir}")
        print(f"📋 Logs directory: {logs_dir}")
        
        # Setup configuration
        config_path = setup_config(data_dir)
        
        # Initialize monitor service with binary-friendly paths
        print("🔧 Initializing monitor service...")
        monitor_service = MonitorService(str(config_path))
        
        # Start web interface
        print("🌐 Starting web interface...")
        web_interface = WebInterface(monitor_service)
        
        print("✅ RepoSense AI started successfully!")
        print(f"🌍 Web interface available at: http://localhost:{monitor_service.config.web_port}")
        print("📖 Open your browser and navigate to the URL above")
        print("⏹️  Press Ctrl+C to stop")
        
        # Start the web interface (this will block)
        web_interface.run()
        
    except KeyboardInterrupt:
        print("\n⏹️  Shutting down RepoSense AI...")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error starting RepoSense AI: {e}")
        print("📋 Check the logs for more details")
        sys.exit(1)


if __name__ == "__main__":
    main()
